import 'dart:io';

import 'package:adaptive_action_sheet/adaptive_action_sheet.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';

class PickFileWidget extends StatefulWidget {
  Widget child;

  final void Function(List<String?>? path) onSelectFile;

  PickFileWidget({
    super.key,
    required this.child,
    required this.onSelectFile,
  });

  @override
  State<PickFileWidget> createState() => _PickFileWidgetState();
}

class _PickFileWidgetState extends State<PickFileWidget> {
  List<XFile> imagePathXFile = [];

  List<PlatformFile> imagePathTransaction = [];

  confirmImage(List<PlatformFile> files,
      {required List<XFile> imagePathTransactionXFile}) {
    imagePathTransaction = files;
    imagePathXFile = imagePathTransactionXFile;

    if (imagePathTransaction.isNotEmpty) {
      widget.onSelectFile(imagePathTransaction.map((e) => e.path).toList());
    }
    if (imagePathXFile.isNotEmpty) {
      widget.onSelectFile(imagePathXFile.map((e) => e.path).toList());
    }
    setState(() {});
  }

  clearImage() {
    imagePathTransaction.clear();
    imagePathXFile.clear();
  }

  Future<FilePickerResult?> pickIdImg(
      {required FileType type, bool allowMultiple = false}) async {
    if (Platform.isIOS) {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: type,
          allowMultiple: allowMultiple,
          allowedExtensions: type != FileType.custom
              ? null
              : [
                  'jpg',
                  'png',
                  'jpeg',
                  'gif',
                  "tif",
                  'tiff',
                  'rar',
                  'doc',
                  "docx",
                  "pdf"
                ]);
      setState(() {});
      return result;
    } else {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowMultiple: allowMultiple,
          allowedExtensions: type != FileType.custom
              ? type == FileType.image
                  ? [
                      'jpg',
                      'png',
                      'jpeg',
                      'gif',
                    ]
                  : null
              : [
                  'jpg',
                  'png',
                  'jpeg',
                  'gif',
                  "tif",
                  'tiff',
                  'rar',
                  'doc',
                  "docx",
                  "pdf"
                ]);
      setState(() {});
      return result;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        clearImage();
        await checkAndRequestCameraPermissions().then((value) async {
          if (value == true) {
            showAdaptiveActionSheet(
              context: context,
              androidBorderRadius: 15,
              actions: <BottomSheetAction>[
                BottomSheetAction(
                    title: Row(children: [
                      Icon(Icons.camera,
                          size: 22, color: Theme.of(context).primaryColor),
                      const SizedBox(
                        height: 10,
                        width: 10,
                      ),
                      // osAddHorizontalSpace(),
                      TextApp(
                        text:(context.translate.camera),
                        style: context.textStyle.copyWith(
                          color: context.color.textColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        // fontSize: 16,
                        // color:
                        // AppColors.black,
                      )
                    ]),
                    onPressed: (context) async {
                      final ImagePicker picker = ImagePicker();
                      Navigator.pop(context);
                      await picker.pickImage(source: ImageSource.camera).then(
                          (value) => confirmImage([],
                              imagePathTransactionXFile:
                                  value == null ? [] : [value]));

                      setState(() {});
                    }),
                BottomSheetAction(
                    title: Row(children: [
                      Icon(Icons.image_outlined,
                          size: 22, color: Theme.of(context).primaryColor),
                      const SizedBox(
                        height: 10,
                        width: 10,
                      ),
                      TextApp(
                        text: context.translate.lblGallery,
                        style: context.textStyle.copyWith(
                          color: context.color.textColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        // fontSize: 16,
                        // color:
                        // AppColors.black,
                      )
                    ]),
                    onPressed: (context) async {
                      Navigator.pop(context);

                      pickIdImg(type: FileType.image).then((value) {
                        //
                        confirmImage(value?.files ?? [],
                            imagePathTransactionXFile: []);

                        setState(() {});
                      });
                    }),
                BottomSheetAction(
                    title: Row(children: [
                      Icon(CupertinoIcons.doc,
                          size: 22, color: Theme.of(context).primaryColor),
                      const SizedBox(
                        height: 10,
                        width: 10,
                      ),
                      TextApp(
                       text:  context.translate.document,
                        style: context.textStyle.copyWith(
                          color: context.color.textColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        // fontSize: 16,
                        // color:
                        // AppColors.black,
                      )
                    ]),
                    onPressed: (context) {
                      Navigator.pop(context);
                      pickIdImg(type: FileType.custom).then((value) {
                        //
                        confirmImage(value?.files ?? [],
                            imagePathTransactionXFile: []);

                        setState(() {});
                      });
                    }),
              ],
              cancelAction: CancelAction(
                  title: TextApp(
               text:  context.translate.cancel,
                style: context.textStyle.copyWith(
                  color: context.color.textColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              )),
            );
          } else {
            // ServiceLocator.instance<Snackbars>().error(
            //   context: context,
            //   message: context.translate
            //       .please_allow_Camera_permission, //"يجب اختيار اسم الطفل",
            // );

            await Future.delayed(Duration(seconds: 1));
            await openAppSettings();
          }
        });
      },
      child: widget.child,
    );
  }
}

Future<bool> checkAndRequestCameraPermissions() async {
  await [Permission.camera].request();
  PermissionStatus permission = await Permission.camera.status;
  if (permission != PermissionStatus.granted) {
    Map<Permission, PermissionStatus> permissions =
        await [Permission.camera].request();
    return permissions[Permission.camera] == PermissionStatus.granted;
  } else {
    return true;
  }
}
