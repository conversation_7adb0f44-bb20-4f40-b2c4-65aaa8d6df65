import 'package:cached_network_image/cached_network_image.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/presentation/widget/cached_network_image_utill.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/presentation/shimmer_componant/user_concerns_shimmer_component.dart';
import 'package:clean_arc/features/treatment_feature/controller/treatment_cubit.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/treatment_model.dart';
import 'package:clean_arc/features/treatment_feature/presentation/shimmer_componant/user_treatment_shimmer_component.dart';
import 'package:clean_arc/features/user_layout/lip_feature/controller/lib_controller.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/termination_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UserTreatmentComponent extends StatefulWidget {
  const UserTreatmentComponent({super.key});

  @override
  State<UserTreatmentComponent> createState() => _UserTreatmentComponentState();
}

class _UserTreatmentComponentState extends State<UserTreatmentComponent> {
  @override
  void initState() {
    context.read<TreatmentCubit>().fetchTreatment();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 16,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            TextApp(
              text: 'العلاج النفسي الإلكتروني',
              style: context.textStyle.copyWith(
                fontSize: AppDimensions.fontSizeLarge,
                fontWeight: FontWeightHelper.bold,
              ),
            ),
            SizedBox(
              height: 5,
            ),
            TextApp(
              text: 'علاج نفسي كامل. عبر جلسات مسجله',
              style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeDefault,
                  fontWeight: FontWeightHelper.bold,
                  color: context.color.grayColor),
            ),
          ],
        ),
        SizedBox(
          height: 10,
        ),
        BlocBuilder<TreatmentCubit, TreatmentState>(builder: (context, state) {
          if (state.errorTreatment != null) {
            return CustomErrorWidget(
              failure: state.errorTreatment,
              onPressed: () {
                context.read<TreatmentCubit>().fetchTreatment();
              },
            );
          } else if (state.isLoadingTreatment == true) {
            return UserTreatmentShimmerComponent();
          }
          return Container(
            height: 200,
            child: ListView.separated(
              separatorBuilder: (context, index) => SizedBox(
                width: 5,
              ),
              scrollDirection: Axis.horizontal,
              itemCount: state.successTreatment?.length ?? 0,
              itemBuilder: (context, index) {
                TreatmentModel? concern = state.successTreatment?[index];

                return InkWell(
                  onTap: () {
                    context
                        .pushRoute(TreatmentVideoViewRoute(id: concern!.id!));
                  },
                  child: Stack(
                    children: [
                      Column(
                        children: [
                          Center(
                            child: Container(
                              height: 150,
                              width: 150,
                              child: Center(
                                  child: CustomCachedNetworkImage(
                                      borderRadius: 10,
                                      imageUrl: concern?.image ?? '')),
                            ),
                          ),
                          Center(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 8.0),
                                child: Container(
                                  width: 150,
                                  child: TextApp(
                                    text: concern?.text ?? '',
                                    style: context.textStyle.copyWith(
                                      fontSize: AppDimensions.fontSizeDefault,
                                      // color: context.color.whiteColor,
                                      fontWeight: FontWeightHelper.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          )),
                          // Center(
                          //   child: Container(
                          //     decoration: BoxDecoration(
                          //         borderRadius: BorderRadius.circular(10),
                          //         color: context.color.textColor?.withOpacity(.5)),
                          //     height: 150,
                          //     width: 150,
                          //     child: Padding(
                          //       padding: const EdgeInsets.all(8.0),
                          //       child: Center(
                          //           child: Column(
                          //         crossAxisAlignment: CrossAxisAlignment.start,
                          //         mainAxisAlignment: MainAxisAlignment.end,
                          //         children: [
                          //           Padding(
                          //             padding: const EdgeInsets.all(8.0),
                          //             child: TextApp(
                          //               text: concern?.text ?? '',
                          //               style: context.textStyle.copyWith(
                          //                 fontSize: AppDimensions.fontSizeDefault,
                          //                 color: context.color.whiteColor,
                          //                 fontWeight: FontWeightHelper.bold,
                          //               ),
                          //               textAlign: TextAlign.center,
                          //             ),
                          //           ),
                          //         ],
                          //       )),
                          //     ),
                          //   ),
                          // )
                        ],
                      ),

                      InkWell(
                        onTap: () {
                          BookManage.toggleTreatment(
                              TreatmentCashModel.fromJson(concern?.toJson() ?? {}));
                        },
                        child: ValueListenableBuilder<Set<int>>(
                          valueListenable: BookManage.savedTreatments,
                          builder: (context, savedBooks, child) {
                            return Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Icon(
                                savedBooks.contains(concern?.id)
                                    ? Icons.bookmark
                                    : Icons.bookmark_border,
                                color: context.color.yellowColor,
                              ),
                            );
                          },
                        ),
                      )

                    ],
                  ),
                );
              },
            ),
          );
        }),
      ],
    );
  }
}
