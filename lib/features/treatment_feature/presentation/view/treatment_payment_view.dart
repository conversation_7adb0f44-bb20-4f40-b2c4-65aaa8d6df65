import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/treatment_model.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_paypal_payment/flutter_paypal_payment.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

@RoutePage()
class TreatmentPaymentView extends StatefulWidget {
  final int treatmentId;
  final TreatmentModel treatmentModel;

  const TreatmentPaymentView({
    Key? key,
    required this.treatmentId,
    required this.treatmentModel,
  }) : super(key: key);

  @override
  State<TreatmentPaymentView> createState() => _TreatmentPaymentViewState();
}

class _TreatmentPaymentViewState extends State<TreatmentPaymentView> {
  double get originalPrice =>
      double.tryParse(widget.treatmentModel.price ?? '0') ?? 0;

  double get discountedPrice {
    final user = context.read<AuthCubit>().state.successLogin?.user;
    final treatmentFeature = user?.subscription?.features?.firstWhereOrNull(
      (feature) => feature.featureType == 'treatment',
    );

    if (treatmentFeature != null &&
        treatmentFeature.featureDiscountPercentage != null) {
      final discount =
          double.tryParse(treatmentFeature.featureDiscountPercentage!) ?? 0;
      return originalPrice * (1 - discount / 100);
    }

    return originalPrice;
  }

  bool get hasDiscount {
    final user = context.read<AuthCubit>().state.successLogin?.user;
    return user?.subscription?.features
            ?.any((feature) => feature.featureType == 'treatment') ??
        false;
  }

  Future<void> _confirmPurchase() async {
    try {
      final authCubit = context.read<AuthCubit>();
      final token =
          authCubit.state.successLogin?.token ?? currentUser?.value.token;

      final response = await http.post(
        Uri.parse('https://api.arab-cbt.com/videos/purchases'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({'video_id': widget.treatmentId}),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Refresh user data to get updated purchases
        final userId = authCubit.state.successLogin?.user?.id ??
            currentUser?.value.user?.id;
        if (userId != null) {
          await authCubit.refreshUserData(userId);
        }

        if (mounted) {
          context.router
              .popAndPush(TreatmentVideoViewRoute(id: widget.treatmentId));
        }
      } else {
        throw Exception('Purchase confirmation failed');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تأكيد الشراء'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: context.color.primaryColor?.withOpacity(.1),
          ),
          child: TextApp(
            text: '💎 عرض لمرة واحدة فقط',
            style: context.textStyle.copyWith(
              fontSize: 14,
              fontWeight: FontWeightHelper.bold,
              color: context.color.primaryColor,
            ),
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextApp(
                        text: widget.treatmentModel.text ?? '',
                        style: context.textStyle.copyWith(
                          fontSize: 20,
                          fontWeight: FontWeightHelper.bold,
                        ),
                      ),
                      SizedBox(height: 8),
                      if (widget.treatmentModel.description != null)
                        Html(data: widget.treatmentModel.description!),
                      // TextApp(
                      //   text: widget.treatmentModel.description!,
                      //   style: context.textStyle.copyWith(
                      //     fontSize: 14,
                      //     color: context.color.grayColor,
                      //   ),
                      // ),
                      // SizedBox(height: 16),
                      // Container(
                      //   width: double.infinity,
                      //   height: 120,
                      //   decoration: BoxDecoration(
                      //     borderRadius: BorderRadius.circular(8),
                      //     gradient: LinearGradient(
                      //       colors: [
                      //         context.color.primaryColor!.withOpacity(0.8),
                      //         context.color.primaryColor!,
                      //       ],
                      //       begin: Alignment.topLeft,
                      //       end: Alignment.bottomRight,
                      //     ),
                      //   ),
                      //   child: Center(
                      //     child: Icon(
                      //       Icons.play_circle_filled,
                      //       color: Colors.white,
                      //       size: 60,
                      //     ),
                      //   ),
                      // ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 24),
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextApp(
                        text: 'تفاصيل السعر',
                        style: context.textStyle.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeightHelper.bold,
                        ),
                      ),
                      SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextApp(
                            text: 'السعر الأصلي',
                            style: context.textStyle.copyWith(
                              fontSize: 16,
                              decoration: hasDiscount
                                  ? TextDecoration.lineThrough
                                  : null,
                              color:
                                  hasDiscount ? context.color.grayColor : null,
                            ),
                          ),
                          TextApp(
                            text: '${originalPrice.toStringAsFixed(2)} NIS',
                            style: context.textStyle.copyWith(
                              fontSize: 16,
                              decoration: hasDiscount
                                  ? TextDecoration.lineThrough
                                  : null,
                              color:
                                  hasDiscount ? context.color.grayColor : null,
                            ),
                          ),
                        ],
                      ),
                      if (hasDiscount) ...[
                        SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextApp(
                              text: 'السعر بعد الخصم',
                              style: context.textStyle.copyWith(
                                fontSize: 16,
                                fontWeight: FontWeightHelper.bold,
                                color: context.color.primaryColor,
                              ),
                            ),
                            TextApp(
                              text: '${discountedPrice.toStringAsFixed(2)} NIS',
                              style: context.textStyle.copyWith(
                                fontSize: 16,
                                fontWeight: FontWeightHelper.bold,
                                color: context.color.primaryColor,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: TextApp(
                            text: 'تم تطبيق خصم الاشتراك',
                            style: context.textStyle.copyWith(
                              fontSize: 12,
                              color: Colors.green,
                              fontWeight: FontWeightHelper.medium,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              if (!hasDiscount) ...[
                SizedBox(height: 16),
                Card(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: context.color.primaryColor,
                          size: 32,
                        ),
                        SizedBox(height: 8),
                        TextApp(
                          text:
                              'اشترك في الموقع واحصل على خصومات مميزة على العلاج النفسي',
                          style: context.textStyle.copyWith(
                            fontSize: 14,
                            color: context.color.primaryColor,
                            fontWeight: FontWeightHelper.medium,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
              SizedBox(
                height: 10,
              ),
              Container(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () => _processPayment(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.color.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: TextApp(
                    text: 'ادفع الآن NIS${discountedPrice.toStringAsFixed(2)}',
                    style: context.textStyle.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeightHelper.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _processPayment() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (BuildContext context) => PaypalCheckoutView(
          sandboxMode: false,
          clientId:
              "AUcNSvCjDRnK-phS_OQHpQVPvQhxN_iGuJXkOUeJ0KX36RgqJNG7ihSpMu6X2dAnjpF2sUkt_2TZxxE6",
          secretKey:
              "EFvKF1GPq_l1gaRNyXOrp_HO8vi2lYVSD2PXn2R3ZjsAHJ_OAEj7EcipEP2U95hbhbX3xrYjyGd3eeNf",
          transactions: [
            {
              "amount": {
                "total": discountedPrice.toStringAsFixed(2),
                "currency": "USD",
                "details": {
                  "subtotal": discountedPrice.toStringAsFixed(2),
                  "shipping": '0',
                  "shipping_discount": 0
                }
              },
              "description": "علاج نفسي: ${widget.treatmentModel.text}",
              "item_list": {
                "items": [
                  {
                    "name": widget.treatmentModel.text ?? "علاج نفسي",
                    "quantity": 1,
                    "price": discountedPrice.toStringAsFixed(2),
                    "currency": "USD"
                  }
                ],
              }
            }
          ],
          note: "شراء علاج نفسي من تطبيق أجوري",
          onSuccess: (Map params) async {
            print("Payment Success: $params");
            await _confirmPurchase();
          },
          onError: (error) {
            print("Payment Error: $error");
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في عملية الدفع'),
                backgroundColor: Colors.red,
              ),
            );
          },
          onCancel: () {
            print("Payment Cancelled");
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إلغاء عملية الدفع'),
                backgroundColor: Colors.orange,
              ),
            );
          },
        ),
      ),
    );
  }
}
