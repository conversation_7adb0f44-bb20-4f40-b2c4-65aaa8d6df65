import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

@RoutePage()
class TreatmentVideoPlayerView extends StatefulWidget {
  final String title;
  final String videoUrl;
  final String treatmentTitle;

  const TreatmentVideoPlayerView({
    Key? key,
    required this.title,
    required this.videoUrl,
    required this.treatmentTitle,
  }) : super(key: key);

  @override
  State<TreatmentVideoPlayerView> createState() =>
      _TreatmentVideoPlayerViewState();
}

class _TreatmentVideoPlayerViewState extends State<TreatmentVideoPlayerView> {
  VideoPlayerController? _controller;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    try {
      final videoUrl = widget.videoUrl.startsWith('http')
          ? widget.videoUrl
          : 'https://api.arab-cbt.com${widget.videoUrl}';

      _controller = VideoPlayerController.network(videoUrl);
      await _controller!.initialize();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextApp(
              text: widget.title,
              style: context.textStyle.copyWith(
                fontSize: 16,
                fontWeight: FontWeightHelper.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            TextApp(
              text: widget.treatmentTitle,
              style: context.textStyle.copyWith(
                fontSize: 12,
                color: context.color.grayColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          Container(
            width: double.infinity,
            height: 250,
            color: Colors.black,
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: context.color.primaryColor,
                    ),
                  )
                : _hasError
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.white,
                              size: 48,
                            ),
                            SizedBox(height: 16),
                            TextApp(
                              text: 'خطأ في تحميل الفيديو',
                              style: context.textStyle.copyWith(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                            SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  _isLoading = true;
                                  _hasError = false;
                                });
                                _initializeVideo();
                              },
                              child: TextApp(
                                text: 'إعادة المحاولة',
                                style: context.textStyle.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : Stack(
                        children: [
                          Center(
                            child: AspectRatio(
                              aspectRatio: _controller!.value.aspectRatio,
                              child: VideoPlayer(_controller!),
                            ),
                          ),
                          Center(
                            child: IconButton(
                              onPressed: () {
                                setState(() {
                                  if (_controller!.value.isPlaying) {
                                    _controller!.pause();
                                  } else {
                                    _controller!.play();
                                  }
                                });
                              },
                              icon: Icon(
                                _controller!.value.isPlaying
                                    ? Icons.pause_circle_filled
                                    : Icons.play_circle_filled,
                                color: Colors.white,
                                size: 64,
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.bottomCenter,
                                  end: Alignment.topCenter,
                                  colors: [
                                    Colors.black.withOpacity(0.8),
                                    Colors.transparent,
                                  ],
                                ),
                              ),
                              child: VideoProgressIndicator(
                                _controller!,
                                allowScrubbing: true,
                                colors: VideoProgressColors(
                                  playedColor: context.color.primaryColor!,
                                  bufferedColor: Colors.white.withOpacity(0.3),
                                  backgroundColor:
                                      Colors.white.withOpacity(0.1),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextApp(
                    text: widget.title,
                    style: context.textStyle.copyWith(
                      fontSize: 20,
                      fontWeight: FontWeightHelper.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  TextApp(
                    text: 'من: ${widget.treatmentTitle}',
                    style: context.textStyle.copyWith(
                      fontSize: 14,
                      color: context.color.grayColor,
                    ),
                  ),
                  // SizedBox(height: 16),
                  // Container(
                  //   width: double.infinity,
                  //   padding: EdgeInsets.all(16),
                  //   decoration: BoxDecoration(
                  //     color: context.color.primaryColor?.withOpacity(0.1),
                  //     borderRadius: BorderRadius.circular(12),
                  //     border: Border.all(
                  //       color: context.color.primaryColor!.withOpacity(0.3),
                  //     ),
                  //   ),
                  //   child: Column(
                  //     crossAxisAlignment: CrossAxisAlignment.start,
                  //     children: [
                  //       Row(
                  //         children: [
                  //           Icon(
                  //             Icons.info_outline,
                  //             color: context.color.primaryColor,
                  //             size: 20,
                  //           ),
                  //           SizedBox(width: 8),
                  //           TextApp(
                  //             text: 'نصائح للاستفادة القصوى',
                  //             style: context.textStyle.copyWith(
                  //               fontSize: 16,
                  //               fontWeight: FontWeightHelper.bold,
                  //               color: context.color.primaryColor,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //       SizedBox(height: 8),
                  //       TextApp(
                  //         text:
                  //             '• شاهد الفيديو في مكان هادئ ومريح\n• خذ ملاحظات أثناء المشاهدة\n• طبق التمارين المذكورة في حياتك اليومية\n• لا تتردد في إعادة مشاهدة الأجزاء المهمة',
                  //         style: context.textStyle.copyWith(
                  //           fontSize: 14,
                  //           color: context.color.primaryColor,
                  //           height: 1.5,
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
