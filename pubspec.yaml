name: clean_arc
description: "A new Flutter project."

publish_to: 'none'

version: 1.0.0+2

environment:
  sdk: '>=3.2.3  <4.0.0'


dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2


  # start Core
  cached_network_image: ^3.3.1
  connectivity_plus: ^5.0.2
  just_audio: ^0.9.41


  hive: ^2.0.4
  hive_flutter: ^1.1.0

  flutter_dotenv: ^5.1.0
  flutter_screenutil: ^5.9.0
  flutter_gen: ^5.4.0
  flutter_svg: ^2.0.10+1
  flutter_html:
  flutter_iconly: ^1.0.2
  #  auto_route: ^7.9.0
  auto_route: ^8.1.2
  flutter_pin_code_fields: ^2.2.0
  flutter_localization: ^0.2.0
  shimmer: ^3.0.0
  pin_code_fields: ^8.0.1
#  google_maps_flutter: ^2.5.3
  google_places_autocomplete_text_field: ^0.1.3
  geocoding: ^3.0.0
  dotted_border: ^2.1.0
  geolocator: ^11.0.0
  u_credit_card: ^1.1.0
  intl: ^0.19.0
  loading_animation_widget: ^1.2.1
  collection:

  get_it: ^7.6.7
  #  move_to_background: ^1.0.2
  injectable: ^2.4.0
  #Api
  curl_logger_dio_interceptor: ^1.0.0
  hexcolor: ^3.0.1
  dio: ^5.4.2
  logger: ^2.2.0
  retrofit: ^4.1.0
  dartz: ^0.10.1
  better_player_plus: ^1.0.8
  video_player: ^2.9.2
  video_thumbnail: ^0.5.3
#  vimeo_video_player: ^1.0.1
  video_thumbnail_imageview: ^0.0.6
  # annotation
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  flutter_polyline_points: ^2.1.0
  #  email_validator: ^2.1.17
  top_snackbar_flutter: ^3.1.0

  # end Core
  url_launcher: ^6.2.6


  flutter_otp_text_field: ^1.1.2+1

  #  mifare_nfc_reader: ^0.0.9

  # Ui widget
  animate_do: ^3.3.2
  flutter_animate: ^4.5.0

  carousel_slider: ^4.2.1
  extended_nested_scroll_view: ^6.2.1
  page_flip: ^0.2.3

  #  state management
  flutter_bloc: ^8.1.5

  #local database
  shared_preferences: ^2.2.2
  lottie: ^3.1.2
  flutter_typeahead: ^5.2.0
  qr_code_scanner:
    path: packages/qr_code_scanner
  country_code_picker: ^3.0.0
  get_storage: ^2.1.1

dev_dependencies:
  #  ============================ Start dev Core Package ========
  #  dart run build_runner build --delete-conflicting-outputs ========
  auto_route_generator: ^8.0.0
  very_good_analysis: ^5.1.0
  flutter_launcher_icons: ^0.13.1
  injectable_generator: ^2.6.1
  build_runner: ^2.4.9
  hive_generator: ^2.0.0

  chewie: ^1.10.0
  flutter_dash: ^1.0.0
  freezed: ^2.4.7
  flutter_gen_runner:
  retrofit_generator: ^8.1.0
  json_serializable: ^6.7.1
  flutter_staggered_grid_view: ^0.7.0
  dropdown_search: ^5.0.6
  adoptive_calendar: ^0.1.9
  #  ============================ file picker Package ========
  adaptive_action_sheet: ^2.0.3
  permission_handler: ^11.0.0
  file_picker: ^8.0.0+1
  image_picker: ^1.1.2
  google_fonts: ^6.2.1
  google_nav_bar: ^5.0.7
  percent_indicator: ^4.2.3
  flutter_paypal_payment: ^1.0.8
  flutter_html: ^3.0.0


  #  ============================ End dev Core Package ========
dependency_overrides:
  intl: ^0.19.0
  meta: ^1.12.0




flutter_gen:
  output: lib/core/presentation/util/style/images
  line_length: 80
  integrations:
    flutter_svg: true
    rive: true
    lottie: true

  assets:
    enabled: true
    outputs:
      class_name: AppImages
      package_parameter_enabled: false
      style: dot-delimiter


  fonts:
    enabled: true
    outputs:
      class_name: AppFonts

  #  ============================ End dev Core Package ========
flutter_launcher_icons:
  android: "ic_launcher"
  ios: true
  image_path: "assets/images/core/logos/logoColum.png"
  min_sdk_android: 21 #


  flutter_test:
    sdk: flutter
#  flutter_lints: ^2.0.0
flutter:
  generate: true
  uses-material-design: true


  assets:
    - .env.prod
    - assets/images/core/
    - assets/images/core/onboarding/
    - assets/images/core/logos/
    - assets/images/svg/
    - assets/images/demo/
    - assets/images/svg_icon/
    - assets/images/salim_icon/ 
    - shorebird.yaml
  #    - assets/fonts/arabic/
  #    - assets/fonts/english/

  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg


  fonts:
    # Arabic Font
    - family: Cairo
      fonts:
        - asset: assets/fonts/arabic/cairo/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/arabic/cairo/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/arabic/cairo/Cairo-Regular.ttf
          weight: 400
    # English Font
    - family: montserrat
      fonts:
        - asset: assets/fonts/english/montserrat/Montserrat-Bold.ttf
          weight: 700
        - asset: assets/fonts/english/montserrat/Montserrat-Medium.ttf
          weight: 500
        - asset: assets/fonts/english/montserrat/Montserrat-Regular.ttf
          weight: 400

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
